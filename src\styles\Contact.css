/* Contact Page Styles */
.contact-page {
  padding: var(--space-24) 0;
  min-height: calc(100vh - 80px);
  background: var(--primary-bg);
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: var(--space-16);
 
  margin-left: auto;
  margin-right: auto;
}

.page-title {
  font-size: var(--text-6xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: var(--text-xl);
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact Info Grid */
.contact-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-16);
}

.contact-info-card {
  background: var(--secondary-bg);
  padding: var(--space-6);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  text-align: center;
  transition: var(--transition-normal);
}

.contact-info-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.info-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-3);
  display: block;
}

.info-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.info-details p {
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
  line-height: 1.5;
}

.info-action {
  margin-top: var(--space-4);
  padding: var(--space-2) var(--space-4);
  background: var(--accent-primary);
  color: var(--primary-bg);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
}

.info-action:hover {
  background: var(--accent-secondary);
}

/* Contact Content */
.contact-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-12);
  align-items: start;
}

/* Contact Form Section */
.contact-form-section {
  background: var(--secondary-bg);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
}

.contact-form-section h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

/* Form Styles */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.form-input,
.form-select,
.form-textarea {
  padding: var(--space-3);
  background: var(--primary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-textarea::placeholder {
  color: var(--text-muted);
}

/* Submit Button */
.submit-button {
  padding: var(--space-4) var(--space-8);
  background: var(--accent-gradient);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

.submit-button:hover:not(:disabled) {
  background: var(--accent-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Contact Details Section */
.contact-details-section {
  background: var(--glass-bg);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  height: fit-content;
}

.contact-details-section h2 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
}

/* Benefits List */
.benefits-list {
  margin-bottom: var(--space-8);
}

.benefit-item {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  align-items: flex-start;
}

.benefit-icon {
  font-size: var(--text-2xl);
  flex-shrink: 0;
}

.benefit-item h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.benefit-item p {
  color: var(--text-secondary);
  line-height: 1.5;
  font-size: var(--text-sm);
}

/* Contact CTA */
.contact-cta {
  text-align: center;
  padding: var(--space-6);
  background: var(--secondary-bg);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-secondary);
}

.contact-cta h3 {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.contact-cta p {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  line-height: 1.5;
}

.schedule-button {
  padding: var(--space-3) var(--space-6);
  background: var(--accent-secondary);
  color: var(--primary-bg);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
}

.schedule-button:hover {
  background: var(--accent-primary);
}

/* Success Message */
.success-message {
  text-align: center;
  padding: var(--space-16);
  background: var(--secondary-bg);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-primary);
  max-width: 600px;
  margin: var(--space-16) auto;
}

.success-icon {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-4);
}

.success-message h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.success-message p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-8);
}

.back-button {
  padding: var(--space-3) var(--space-6);
  background: var(--accent-primary);
  color: var(--primary-bg);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
}

.back-button:hover {
  background: var(--accent-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-page {
    padding: var(--space-16) 0;
  }
  
  .page-title {
    font-size: var(--text-4xl);
  }
  
  .page-subtitle {
    font-size: var(--text-lg);
  }
  
  .contact-info-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .contact-form-section,
  .contact-details-section {
    padding: var(--space-6);
  }
  
  .contact-form-section h2 {
    font-size: var(--text-2xl);
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: var(--text-3xl);
  }
  
  .contact-info-card {
    padding: var(--space-4);
  }
  
  .contact-form-section,
  .contact-details-section {
    padding: var(--space-4);
  }
  
  .submit-button {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }
}
