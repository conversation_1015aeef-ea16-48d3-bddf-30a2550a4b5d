/* Image Upload Component Styles */
.image-upload-container {
  width: 100%;
  margin-bottom: var(--space-6);
}

.upload-section {
  margin-bottom: var(--space-4);
}

.upload-area {
  border: 2px dashed var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--bg-secondary);
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: var(--accent-primary);
  background: var(--bg-tertiary);
  transform: translateY(-2px);
}

.upload-area.drag-active {
  border-color: var(--accent-primary);
  background: rgba(var(--accent-primary-rgb), 0.1);
  transform: scale(1.02);
}

.upload-area.uploading {
  pointer-events: none;
  opacity: 0.7;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.6;
}

.upload-content h3 {
  color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: 600;
  margin: 0;
}

.upload-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: var(--text-sm);
}

.upload-info {
  font-size: var(--text-xs) !important;
  opacity: 0.7;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
}

.upload-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.upload-loading p {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin: 0;
}

/* Existing Images Section */
.existing-images {
  margin-top: var(--space-6);
}

.existing-images h4 {
  color: var(--text-primary);
  font-size: var(--text-base);
  font-weight: 600;
  margin-bottom: var(--space-3);
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--space-3);
  max-height: 300px;
  overflow-y: auto;
  padding: var(--space-2);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
}

.image-preview {
  position: relative;
  aspect-ratio: 1;
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  transition: all var(--transition-normal);
}

.image-preview:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.remove-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  opacity: 0;
  transform: scale(0.8);
}

.image-preview:hover .remove-image-btn {
  opacity: 1;
  transform: scale(1);
}

.remove-image-btn:hover {
  background: rgba(255, 0, 0, 1);
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-area {
    padding: var(--space-6);
    min-height: 150px;
  }
  
  .upload-icon {
    font-size: 2rem;
  }
  
  .upload-content h3 {
    font-size: var(--text-base);
  }
  
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: var(--space-2);
  }
}

@media (max-width: 480px) {
  .upload-area {
    padding: var(--space-4);
    min-height: 120px;
  }
  
  .upload-content {
    gap: var(--space-2);
  }
  
  .upload-content h3 {
    font-size: var(--text-sm);
  }
  
  .upload-content p {
    font-size: var(--text-xs);
  }
}

/* Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .upload-area {
    background: var(--bg-primary);
  }
  
  .upload-area:hover {
    background: var(--bg-secondary);
  }
  
  .images-grid {
    background: var(--bg-primary);
  }
  
  .image-preview {
    background: var(--bg-secondary);
  }
}
