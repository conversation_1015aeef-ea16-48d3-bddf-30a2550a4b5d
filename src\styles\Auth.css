/* Authentication Pages Styles */
.auth-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.auth-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.auth-content {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

.auth-form-section {
  padding: 60px 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-form-container {
  width: 100%;
  max-width: 400px;
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 10px;
  line-height: 1.2;
}

.auth-header p {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

.success-icon,
.error-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

/* Form Styles */
.auth-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.form-input {
  width: 100%;
  padding: 15px 18px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #fafbfc;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.form-input.error:focus {
  border-color: #e74c3c;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: #666;
  padding: 5px;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.password-toggle:hover {
  color: #333;
}

.form-hint {
  display: block;
  color: #666;
  font-size: 0.85rem;
  margin-top: 5px;
}

.form-error {
  display: block;
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 5px;
}

.form-options {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 30px;
}

.forgot-password-link {
  color: #667eea;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password-link:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

/* Button Styles */
.auth-submit-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.auth-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.auth-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.auth-submit-btn:active {
  transform: translateY(0);
}

/* Footer Styles */
.auth-footer {
  text-align: center;
}

.auth-footer p {
  color: #666;
  margin: 0;
}

.auth-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

.resend-link {
  background: none;
  border: none;
  color: #667eea;
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  font-weight: 600;
  margin-left: 10px;
  transition: color 0.3s ease;
}

.resend-link:hover {
  color: #5a6fd8;
}

/* Visual Section */
.auth-visual-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.auth-visual-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.auth-visual-content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 400px;
}

.auth-visual-content h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.3;
}

.auth-visual-content p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 40px;
  line-height: 1.6;
}

.auth-visual-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-item span:last-child {
  font-weight: 500;
  font-size: 1rem;
}

/* Error Message */
.error-message {
  background: #fdf2f2;
  color: #e74c3c;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 25px;
  border: 1px solid #fadbd8;
  font-size: 0.95rem;
  text-align: center;
}

/* Success Content */
.auth-success-content {
  text-align: center;
}

.auth-success-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.auth-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-content {
    grid-template-columns: 1fr;
    min-height: auto;
  }
  
  .auth-visual-section {
    order: -1;
    padding: 40px 30px;
  }
  
  .auth-form-section {
    padding: 40px 30px;
  }
  
  .auth-header h1 {
    font-size: 2rem;
  }
  
  .auth-visual-content h2 {
    font-size: 1.8rem;
  }
  
  .auth-visual-features {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .feature-item {
    flex: 1;
    min-width: 120px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
    padding: 15px 10px;
  }
  
  .feature-item span:last-child {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .auth-page {
    padding: 10px;
  }
  
  .auth-form-section,
  .auth-visual-section {
    padding: 30px 20px;
  }
  
  .auth-header h1 {
    font-size: 1.8rem;
  }
  
  .auth-visual-content h2 {
    font-size: 1.6rem;
  }
}
