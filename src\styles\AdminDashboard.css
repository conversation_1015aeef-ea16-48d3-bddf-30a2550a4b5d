/* Admin Dashboard Styles */
.admin-dashboard {
  min-height: 100vh;
  background: var(--primary-bg);
}

/* Admin Header */
.admin-header {
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  margin-top: 6%;
  padding: var(--space-6) 0;
}

.admin-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-title h1 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-title p {
  color: var(--text-secondary);
  font-size: var(--text-base);
}

.admin-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.btn {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition-normal);
  cursor: pointer;
  border: none;
  font-size: var(--text-sm);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-outline {
  background: transparent;
  color: var(--accent-primary);
  border: 1px solid var(--accent-primary);
}

.btn-outline:hover {
  background: var(--accent-primary);
  color: white;
}

.btn-primary {
  background: var(--accent-gradient);
  color: white;
  border: 1px solid transparent;
}

.btn-primary:hover {
  background: var(--accent-gradient-hover);
  transform: translateY(-1px);
}

.btn-danger {
  background: #ef4444;
  color: white;
  border: 1px solid transparent;
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Admin Content */
.admin-content {
  padding: var(--space-8) 0;
}

.admin-tabs {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-8);
  border-bottom: 1px solid var(--border-primary);
}

.tab-btn {
  padding: var(--space-3) var(--space-6);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  color: var(--accent-primary);
}

.tab-btn.active {
  color: var(--accent-primary);
  border-bottom-color: var(--accent-primary);
}

/* Overview Section */
.overview-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.stat-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: var(--transition-normal);
}

.stat-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.stat-icon {
  font-size: var(--text-4xl);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
}

.stat-content h3 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.stat-content p {
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

/* Quick Actions */
.quick-actions h2 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.action-btn {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--text-primary);
  transition: var(--transition-normal);
  cursor: pointer;
}

.action-btn:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.action-icon {
  font-size: var(--text-2xl);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-gradient);
  border-radius: var(--radius-lg);
  color: white;
}

/* Projects Section */
.projects-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h2 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

/* Projects Table */
.projects-table {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--space-4);
  padding: var(--space-4) var(--space-6);
  background: var(--glass-bg);
  border-bottom: 1px solid var(--border-primary);
  font-weight: 600;
  color: var(--text-primary);
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--space-4);
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-secondary);
  transition: var(--transition-normal);
}

.table-row:hover {
  background: var(--glass-bg);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  display: flex;
  align-items: center;
}

.project-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.project-thumbnail {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-md);
  object-fit: cover;
}

.project-details h4 {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.project-details p {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.featured-toggle {
  background: transparent;
  border: none;
  font-size: var(--text-lg);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.featured-toggle:hover {
  background: var(--glass-bg);
}

.featured-toggle.featured {
  color: #fbbf24;
}

.actions {
  display: flex;
  gap: var(--space-2);
}

.action-btn-small {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
  font-size: var(--text-sm);
}

.action-btn-small.view {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.action-btn-small.edit {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.action-btn-small.delete {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.action-btn-small:hover {
  transform: scale(1.1);
}

/* Loading and Error States */
.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
  padding: var(--space-8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-primary);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state h3 {
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.error-state p {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-header-content {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .admin-actions {
    flex-direction: column;
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }

  .table-cell {
    justify-content: space-between;
    padding: var(--space-2) 0;
  }

  .project-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .actions {
    justify-content: flex-end;
  }
}
