/* Completed Projects Section */
.completed-projects {
  padding: var(--space-32) 0;
  background: var(--primary-bg);
  position: relative;
}

.completed-projects::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: radial-gradient(
    ellipse at top left,
    rgba(255, 107, 53, 0.05) 0%,
    transparent 50%
  );
  pointer-events: none;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--text-5xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.title-accent {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--space-3) var(--space-6);
  background: transparent;
  border: 2px solid var(--border-secondary);
  border-radius: var(--radius-full);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.filter-btn:hover {
  border-color: var(--accent-secondary);
  color: var(--text-primary);
}

.filter-btn.active {
  background: var(--accent-secondary);
  border-color: var(--accent-secondary);
  color: var(--primary-bg);
}

/* Projects Grid */
.completed-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-6);
}

/* Project Card */
.completed-card {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: var(--transition-normal);
}

.completed-card:hover {
  border-color: var(--accent-secondary);
  box-shadow: var(--shadow-lg);
}

/* Card Image */
.completed-image {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.completed-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.completed-card:hover .completed-image img {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
}

.completed-card:hover .image-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
}

.view-gallery {
  background: var(--accent-secondary);
  color: var(--primary-bg);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--text-sm);
}

/* Card Content */
.completed-content {
  padding: var(--space-5);
}

.completed-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-2);
}

.completed-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.completed-year {
  background: var(--accent-secondary);
  color: var(--primary-bg);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
}

.completed-location {
  color: var(--text-muted);
  font-size: var(--text-sm);
  margin-bottom: var(--space-4);
}

/* Stats */
.completed-stats {
  display: flex;
  gap: var(--space-6);
  margin-bottom: var(--space-4);
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--accent-secondary);
}

.stat-label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Features */
.completed-features {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.feature-badge {
  background: var(--glass-bg);
  color: var(--text-secondary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  border: 1px solid var(--border-secondary);
}

/* Project Modal */
.project-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
}

.modal-content {
  background: var(--secondary-bg);
  border-radius: var(--radius-2xl);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 1px solid var(--border-primary);
}

.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--accent-secondary);
  color: var(--primary-bg);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: var(--text-2xl);
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--text-primary);
}

.modal-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-2);
  padding: var(--space-6);
  padding-bottom: 0;
}

.modal-gallery img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-lg);
}

.modal-info {
  padding: var(--space-6);
}

.modal-info h3 {
  font-size: var(--text-3xl);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.modal-location {
  color: var(--accent-secondary);
  font-size: var(--text-lg);
  font-weight: 500;
  margin-bottom: var(--space-4);
}

.modal-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.modal-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
}

.modal-feature {
  background: var(--accent-secondary);
  color: var(--primary-bg);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .completed-projects {
    padding: var(--space-20) 0;
  }

  .section-title {
    font-size: var(--text-4xl);
  }

  .completed-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .completed-card {
    max-width: 400px;
    margin: 0 auto;
  }

  .filter-buttons {
    gap: var(--space-2);
  }

  .filter-btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
  }

  .modal-content {
    margin: var(--space-4);
    max-height: 85vh;
  }

  .modal-gallery {
    grid-template-columns: 1fr;
    padding: var(--space-4);
  }

  .modal-info {
    padding: var(--space-4);
  }

  .modal-info h3 {
    font-size: var(--text-2xl);
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: var(--text-3xl);
  }

  .completed-stats {
    justify-content: center;
  }

  .filter-buttons {
    flex-direction: column;
    align-items: center;
  }

  .filter-btn {
    width: 200px;
  }
}
