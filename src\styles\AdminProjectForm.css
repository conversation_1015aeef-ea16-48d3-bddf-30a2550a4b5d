.admin-project-form {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.admin-project-form .admin-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem 0;
}

.admin-project-form .admin-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.admin-project-form .admin-title h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}

.admin-project-form .admin-title p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0.5rem 0 0 0;
}

.admin-project-form .admin-content {
  padding: 3rem 0;
}

.project-form {
  background: var(--bg-primary);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.form-section {
  margin-bottom: 3rem;
}

.form-section h2 {
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  color: #2d3748;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
  color: #1e293b;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-group input[type="checkbox"] {
  width: auto;
  margin-right: 0.5rem;
  accent-color: #4f46e5;
}

.form-group label:has(input[type="checkbox"]) {
  flex-direction: row;
  align-items: center;
  cursor: pointer;
}

/* Features Section */
.features-section {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.add-feature {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.add-feature input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  background: #667eea;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  gap: 0.5rem;
}

.feature-item .remove-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.feature-item .remove-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Amenities Section */
.amenities-section {
  background: #f7fafc;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.add-amenity {
  display: grid;
  grid-template-columns: 2fr 1fr 2fr auto;
  gap: 1rem;
  margin-bottom: 1rem;
}

.add-amenity input {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
}

.amenities-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.amenity-item {
  display: flex;
  align-items: center;
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  gap: 1rem;
}

.amenity-icon {
  font-size: 1.5rem;
  width: 40px;
  text-align: center;
}

.amenity-details {
  flex: 1;
}

.amenity-details strong {
  display: block;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.amenity-details p {
  color: #718096;
  font-size: 0.9rem;
  margin: 0;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: center;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
  margin-top: 2rem;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  min-width: 150px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
}

.btn-outline {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-outline:hover {
  background: white;
  color: #667eea;
}

/* Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error Message */
.error-message {
  background: #fed7d7;
  color: #c53030;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border: 1px solid #feb2b2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-project-form .admin-header-content {
    flex-direction: column;
    text-align: center;
  }

  .project-form {
    padding: 2rem 1.5rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .add-amenity {
    grid-template-columns: 1fr;
  }

  .add-feature {
    flex-direction: column;
  }

  .amenity-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
}
