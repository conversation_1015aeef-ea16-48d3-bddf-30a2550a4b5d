/* Ongoing Projects Section */
.ongoing-projects {
  padding: var(--space-32) 0;
  background: var(--secondary-bg);
  position: relative;
  overflow: hidden;
}

.ongoing-projects::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at top right,
      rgba(0, 212, 255, 0.05) 0%,
      transparent 50%);
  pointer-events: none;
}

/* Section Header */
.section-header {
  text-align: center;
  margin-bottom: var(--space-20);
}

.section-title {
  font-size: var(--text-5xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.title-accent {
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-16);
  justify-content: center;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 1rem;
}

/* Project Card */
.project-card {
  background: var(--card-bg);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  transition: var(--transition-normal);
  position: relative;
}

.project-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-xl);
}

.project-card:hover .card-overlay {
  opacity: 1;
}

/* Card Image */
.card-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.project-card:hover .card-image img {
  transform: scale(1.1);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
}

/* Progress Circle */
.progress-circle {
  top:30%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset 1s ease-in-out;
}

.progress-text {
  position: absolute;
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--text-primary);
}

/* Card Content */
.card-content {
  padding: var(--space-6);
}

.card-header {
  margin-bottom: var(--space-4);
}

.project-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.project-location {
  color: var(--accent-primary);
  font-size: var(--text-sm);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

/* Project Details */
.project-details {
  margin-bottom: var(--space-6);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-primary);
}



.detail-label {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.detail-value {
  color: var(--text-primary);
  font-weight: 500;
}

/* Features */
.project-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
}

.feature-tag {
  background: var(--glass-bg);
  color: var(--text-secondary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  border: 1px solid var(--border-secondary);
  transition: var(--transition-fast);
}

.feature-tag:hover {
  background: var(--accent-primary);
  color: var(--primary-bg);
  border-color: var(--accent-primary);
}

.feature-more {
  background: var(--accent-primary);
  color: var(--primary-bg);
  border-color: var(--accent-primary);
}

/* Card Button */
.card-btn {
  width: 100%;
  justify-content: center;
}

/* Section Footer */
.section-footer {
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ongoing-projects {
    padding: var(--space-20) 0;
  }

  .section-title {
    font-size: var(--text-4xl);
  }

  .section-subtitle {
    font-size: var(--text-base);
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-12);
  }

  .project-card {
    max-width: 400px;
    margin: 0 auto;
  }

  .card-image {
    height: 200px;
  }

  .card-content {
    padding: var(--space-4);
  }

  .project-title {
    font-size: var(--text-xl);
  }
}

@media (max-width: 480px) {
  .section-header {
    margin-bottom: var(--space-12);
  }

  .section-title {
    font-size: var(--text-3xl);
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .project-card {
    max-width: none;
  }

  .card-image {
    height: 180px;
  }

  .project-features {
    gap: var(--space-1);
  }

  .feature-tag {
    font-size: 10px;
    padding: 2px var(--space-2);
  }
}