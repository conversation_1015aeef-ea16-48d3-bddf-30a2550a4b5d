/* Lottie Preloader Styles */
.lottie-preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.preloader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
}

.lottie-container {
  margin-bottom: var(--space-6);
  filter: drop-shadow(0 10px 30px rgba(0, 212, 255, 0.3));
}

.preloader-text {
  max-width: 400px;
}

.preloader-title {
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.preloader-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  font-weight: 500;
}

.loading-bar {
  width: 300px;
  height: 4px;
  background: var(--border-primary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-4);
  position: relative;
}

.loading-progress {
  height: 100%;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
  width: 100%;
  position: relative;
}

.loading-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
  font-weight: 500;
}

/* Background Animation */
.preloader-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: var(--accent-gradient);
}

.bg-circle-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  right: 10%;
  opacity: 0.1;
}

.bg-circle-2 {
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: 15%;
  opacity: 0.08;
}

.bg-circle-3 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 20%;
  opacity: 0.06;
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .preloader-title {
    font-size: var(--text-3xl);
  }
  
  .preloader-subtitle {
    font-size: var(--text-base);
  }
  
  .loading-bar {
    width: 250px;
  }
  
  .lottie-container {
    transform: scale(0.8);
  }
  
  .bg-circle-1 {
    width: 200px;
    height: 200px;
  }
  
  .bg-circle-2 {
    width: 150px;
    height: 150px;
  }
  
  .bg-circle-3 {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .preloader-title {
    font-size: var(--text-2xl);
  }
  
  .loading-bar {
    width: 200px;
  }
  
  .lottie-container {
    transform: scale(0.7);
  }
}
