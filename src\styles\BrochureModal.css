/* Brochure Modal Styles */
.brochure-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
}

.brochure-modal {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid var(--border-primary);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modal-header h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--text-2xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
}

.project-info {
  background: var(--bg-secondary);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-6);
  border: 1px solid var(--border-primary);
}

.project-info h4 {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
}

.project-info p {
  color: var(--text-secondary);
  margin: 0;
  font-size: var(--text-sm);
}

.brochure-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.form-input {
  padding: var(--space-3);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: var(--transition-normal);
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(var(--accent-primary-rgb), 0.1);
}

.form-input::placeholder {
  color: var(--text-tertiary);
}

textarea.form-input {
  resize: vertical;
  min-height: 80px;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  padding: var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  border: 1px solid rgba(239, 68, 68, 0.2);
  margin-bottom: var(--space-2);
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.form-actions .btn {
  flex: 1;
  justify-content: center;
  position: relative;
}

.form-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--space-2);
}

.privacy-notice {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.privacy-notice p {
  margin: 0;
  color: var(--text-tertiary);
  text-align: center;
}

/* Success Modal */
.success-modal {
  max-width: 400px;
  text-align: center;
}

.success-content {
  padding: var(--space-8);
}

.success-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
}

.success-content h3 {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
}

.success-content p {
  color: var(--text-secondary);
  margin: 0 0 var(--space-6) 0;
  line-height: 1.5;
}

.success-animation {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.download-icon {
  font-size: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .brochure-modal-overlay {
    padding: var(--space-2);
  }

  .brochure-modal {
    max-width: none;
    margin: var(--space-2);
  }

  .modal-header,
  .modal-body {
    padding: var(--space-4);
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .brochure-modal-overlay {
    padding: var(--space-1);
  }

  .modal-header,
  .modal-body {
    padding: var(--space-3);
  }

  .project-info {
    padding: var(--space-3);
  }

  .success-content {
    padding: var(--space-6);
  }

  .modal-header h3 {
    font-size: var(--text-lg);
  }
}

/* Animation keyframes */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form validation styles */
.form-input:invalid {
  border-color: rgba(239, 68, 68, 0.5);
}

.form-input:valid {
  border-color: rgba(16, 185, 129, 0.5);
}

/* Focus states for accessibility */
.modal-close:focus,
.form-input:focus,
.btn:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .brochure-modal {
    border: 2px solid var(--text-primary);
  }
  
  .form-input {
    border-width: 2px;
  }
  
  .error-message {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
  
  .download-icon {
    animation: none;
  }
  
  .brochure-modal-overlay,
  .brochure-modal {
    transition: none;
  }
}
